import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { 
  faHome,
  faComment,
  faClockRotateLeft,
  faFolderTree,
  faUser,
  faGear,
  faWifi,
  faInfoCircle,
  faCircle,
  faCheck,
  faPaperclip,
  faEllipsisVertical,
  faPlus,
  faPaperPlane,
  faEye,
  faCode,
  faPlay,
  faCopy,
  faExpand,
  faXmark,
  faSliders,
  faRobot,
  faStar,
  faDownload,
  faKey,
  faArrowLeft,
  faWrench,
  faBars
} from '@fortawesome/free-solid-svg-icons'

interface IconProps {
  className?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  title?: string
}

// Icon size mapping
const sizeMap = {
  xs: 'text-xs',
  sm: 'text-sm', 
  md: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl'
}

// Centralized icon components using FontAwesome with tree-shaking
export const HomeIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faHome} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const ChatIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faComment} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const HistoryIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faClockRotateLeft} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const FilesIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faFolderTree} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const UserIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faUser} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const SettingsIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faGear} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const WifiIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faWifi} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const InfoIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faInfoCircle} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const CircleIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faCircle} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const CheckIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faCheck} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const PaperclipIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faPaperclip} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const MoreIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faEllipsisVertical} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const PlusIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faPlus} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const SendIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faPaperPlane} className={`${sizeMap[size]} ${className}`} title={title} />
)

export const WrenchIcon: React.FC<IconProps> = ({ className = '', size = 'md', title }) => (
  <FontAwesomeIcon icon={faWrench} className={`${sizeMap[size]} ${className}`} title={title} />
)

// Legacy icon mapping for backward compatibility with CDN class names
export const LegacyIcon: React.FC<{ icon: string; className?: string; title?: string }> = ({ 
  icon, 
  className = '', 
  title 
}) => {
  const iconMap: Record<string, any> = {
    'fa-solid fa-home': faHome,
    'fa-solid fa-comment': faComment,
    'fa-solid fa-clock-rotate-left': faClockRotateLeft,
    'fa-solid fa-folder-tree': faFolderTree,
    'fa-solid fa-user': faUser,
    'fa-solid fa-gear': faGear,
    'fa-solid fa-wifi': faWifi,
    'fa-solid fa-info-circle': faInfoCircle,
    'fa-solid fa-circle': faCircle,
    'fa-solid fa-check': faCheck,
    'fa-solid fa-paperclip': faPaperclip,
    'fa-solid fa-ellipsis-vertical': faEllipsisVertical,
    'fa-solid fa-plus': faPlus,
    'fa-solid fa-paper-plane': faPaperPlane,
    'fa-solid fa-eye': faEye,
    'fa-solid fa-code': faCode,
    'fa-solid fa-play': faPlay,
    'fa-solid fa-copy': faCopy,
    'fa-solid fa-expand': faExpand,
    'fa-solid fa-xmark': faXmark,
    'fa-solid fa-sliders': faSliders,
    'fa-solid fa-robot': faRobot,
    'fa-solid fa-star': faStar,
    'fa-solid fa-download': faDownload,
    'fa-solid fa-key': faKey,
    'fa-solid fa-arrow-left': faArrowLeft,
    'fa-solid fa-wrench': faWrench,
    'fa-solid fa-bars': faBars
  }

  const faIcon = iconMap[icon]
  if (!faIcon) {
    console.warn(`Icon not found: ${icon}`)
    return <span className={className} title={title}>?</span>
  }

  return <FontAwesomeIcon icon={faIcon} className={className} title={title} />
}

// Custom SVG icons for cases where FontAwesome doesn't have suitable alternatives
interface CustomIconProps {
  className?: string
  size?: number
}

const CustomIcon: React.FC<CustomIconProps & { children: React.ReactNode }> = ({ 
  className = "h-4 w-4", 
  size, 
  children 
}) => {
  const sizeClass = size ? `h-${size} w-${size}` : className
  return (
    <svg
      className={sizeClass}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      {children}
    </svg>
  )
}

// Keep some custom icons that don't have good FontAwesome equivalents
export const MenuIcon: React.FC<CustomIconProps> = (props) => (
  <CustomIcon {...props}>
    <line x1="4" x2="20" y1="6" y2="6" />
    <line x1="4" x2="20" y1="12" y2="12" />
    <line x1="4" x2="20" y1="18" y2="18" />
  </CustomIcon>
)

export const BotIcon: React.FC<CustomIconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="M12 8V4H8" />
    <rect width="16" height="12" x="4" y="8" rx="2" />
    <path d="m9 16 0 0" />
    <path d="m15 16 0 0" />
  </CustomIcon>
)

export const Loader2Icon: React.FC<CustomIconProps> = (props) => (
  <CustomIcon {...props}>
    <path d="M21 12a9 9 0 11-6.219-8.56" />
  </CustomIcon>
)
